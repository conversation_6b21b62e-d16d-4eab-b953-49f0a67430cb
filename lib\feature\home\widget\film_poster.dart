import 'package:flutter/material.dart';
import 'package:movie_proj/core/my_styles.dart';

class FilmPoster extends StatelessWidget {
  final String imagePath;
  final String title;
  final bool isNetworkImage;

  const FilmPoster({
    super.key,
    required this.imagePath,
    required this.title,
    this.isNetworkImage = false,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Container(
          width: 200,
          height: 300,
          padding: const EdgeInsets.only(top: 255),
          decoration: BoxDecoration(
            image: DecorationImage(
              image: isNetworkImage
                  ? NetworkImage(imagePath) as ImageProvider
                  : AssetImage(imagePath),
              fit: BoxFit.cover,
            ),
          ),
          child: Container(
            color: Colors.black.withOpacity(0.5),
            child: Text(
              title,
              textAlign: TextAlign.center,
              style: MyStyles.title24White400.copyWith(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
