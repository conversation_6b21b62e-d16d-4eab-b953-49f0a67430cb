import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:movie_proj/feature/home/<USER>/top_moves.dart';
import 'package:movie_proj/feature/home/<USER>/top_movies_service.dart';

// States
abstract class TopMoviesState {}

class TopMoviesInitial extends TopMoviesState {}

class TopMoviesLoading extends TopMoviesState {}

class TopMoviesLoaded extends TopMoviesState {
  final List<TopMovie> movies;

  TopMoviesLoaded(this.movies);
}

class TopMoviesError extends TopMoviesState {
  final String message;

  TopMoviesError(this.message);
}

// Cubit
class TopMoviesCubit extends Cubit<TopMoviesState> {

  TopMoviesCubit(this._topMoviesService) : super(TopMoviesInitial());

  Future<void> loadTopMovies() async {
    try {
      emit(TopMoviesLoading());
      final movies = await _topMoviesService.getTopMovies();
      emit(TopMoviesLoaded(movies));
    } catch (e) {
      emit(TopMoviesError('Failed to load top movies: $e'));
    }
  }

  Future<void> addTopMovie(TopMovie movie) async {
    try {
      await _topMoviesService.addTopMovie(movie);
      loadTopMovies(); // Reload the list after adding
    } catch (e) {
      emit(TopMoviesError('Failed to add movie: $e'));
    }
  }
}
