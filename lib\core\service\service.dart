import 'package:movie_proj/feature/home/<USER>/top_movies_service.dart';
import 'package:movie_proj/feature/home/<USER>/top_moves.dart';

/// Convenience function that uses the TopMoviesService
/// This maintains backward compatibility while using the new service
Future<List<TopMovie>> fetchtopmoviesMovies() async {
  final service = TopMoviesService();
  return await service.getTopMovies();
}

Future<List<TopMovie>> topBoxOffice() async {
  final service = TopMoviesService();
  return await service.getTopMovies();
}