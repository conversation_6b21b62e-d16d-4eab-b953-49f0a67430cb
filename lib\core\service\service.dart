import 'package:http/http.dart' as http;
import 'dart:convert';

import 'package:movie_proj/core/const.dart';

Future<void> fetchtopmoviesMovies() async {
  final url = Uri.parse('https://imdb236.p.rapidapi.com/api/imdb/top250-movies');

  final response = await http.get(
    url,
    headers: {
      'X-RapidAPI-Key': xRapidAPIKey,
      'X-RapidAPI-Host': xRapidAPIHost,
    },
  );

  if (response.statusCode == 200) {
    final data = jsonDecode(response.body);
    print(data);
  } else {
    print('Failed to fetch movies: ${response.statusCode}');
  }
}
