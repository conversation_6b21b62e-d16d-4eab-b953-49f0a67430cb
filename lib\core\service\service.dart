import 'package:movie_proj/feature/home/<USER>/top_movies_service.dart';
import 'package:movie_proj/feature/home/<USER>/new_release_service.dart';
import 'package:movie_proj/feature/home/<USER>/top_moves.dart';
import 'package:movie_proj/feature/home/<USER>/new_release_model.dart';

/// Convenience function that uses the TopMoviesService
/// This maintains backward compatibility while using the new service
Future<List<TopMovie>> fetchtopmoviesMovies() async {
  final service = TopMoviesService();
  return await service.getTopMovies();
}

/// Function that fetches top box office movies using the NewReleaseService
Future<List<NewReleaseMovie>> topBoxOffice() async {
  final service = NewReleaseService();
  return await service.getTopBoxOffice();
}

