import 'package:http/http.dart' as http;
import 'dart:convert';

import 'package:movie_proj/core/const.dart';

Future<void> fetchMovies() async {
  final url = Uri.parse('https://imdb8.p.rapidapi.com/auto-complete?');

  final response = await http.get(
    url,
    headers: {
      'X-RapidAPI-Key': xRapidAPIKey,
      'X-RapidAPI-Host': xRapidAPIHost,
    },
  );

  if (response.statusCode == 200) {
    final data = jsonDecode(response.body);
    print(data);
  } else {
    print('Failed to fetch movies: ${response.statusCode}');
  }
}
