class NewReleaseMovie {
  final String id;
  final String url;
  final String primaryTitle;
  final String originalTitle;
  final String type;
  final String description;
  final String primaryImage;
  final List<MovieThumbnail> thumbnails;
  final String trailer;
  final String contentRating;
  final int startYear;
  final int? endYear;
  final String releaseDate;
  final List<String> interests;
  final List<String> countriesOfOrigin;
  final List<String> externalLinks;
  final List<String> spokenLanguages;
  final List<String> filmingLocations;
  final List<ProductionCompany> productionCompanies;
  final int budget;
  final int grossWorldwide;
  final List<String> genres;
  final bool isAdult;
  final int runtimeMinutes;
  final double averageRating;
  final int numVotes;
  final int metascore;
  final int weekendGrossAmount;
  final String weekendGrossCurrency;
  final int lifetimeGrossAmount;
  final String lifetimeGrossCurrency;
  final int weeksRunning;

  NewReleaseMovie({
    required this.id,
    required this.url,
    required this.primaryTitle,
    required this.originalTitle,
    required this.type,
    required this.description,
    required this.primaryImage,
    required this.thumbnails,
    required this.trailer,
    required this.contentRating,
    required this.startYear,
    this.endYear,
    required this.releaseDate,
    required this.interests,
    required this.countriesOfOrigin,
    required this.externalLinks,
    required this.spokenLanguages,
    required this.filmingLocations,
    required this.productionCompanies,
    required this.budget,
    required this.grossWorldwide,
    required this.genres,
    required this.isAdult,
    required this.runtimeMinutes,
    required this.averageRating,
    required this.numVotes,
    required this.metascore,
    required this.weekendGrossAmount,
    required this.weekendGrossCurrency,
    required this.lifetimeGrossAmount,
    required this.lifetimeGrossCurrency,
    required this.weeksRunning,
  });

  factory NewReleaseMovie.fromJson(Map<String, dynamic> json) {
    return NewReleaseMovie(
      id: json['id'] as String? ?? '',
      url: json['url'] as String? ?? '',
      primaryTitle: json['primaryTitle'] as String? ?? '',
      originalTitle: json['originalTitle'] as String? ?? '',
      type: json['type'] as String? ?? 'movie',
      description: json['description'] as String? ?? '',
      primaryImage: json['primaryImage'] as String? ?? '',
      thumbnails: (json['thumbnails'] as List?)
              ?.map((e) => MovieThumbnail.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      trailer: json['trailer'] as String? ?? '',
      contentRating: json['contentRating'] as String? ?? '',
      startYear: json['startYear'] as int? ?? 0,
      endYear: json['endYear'] as int?,
      releaseDate: json['releaseDate'] as String? ?? '',
      interests: (json['interests'] as List?)?.map((e) => e as String).toList() ?? [],
      countriesOfOrigin: (json['countriesOfOrigin'] as List?)?.map((e) => e as String).toList() ?? [],
      externalLinks: (json['externalLinks'] as List?)?.map((e) => e as String).toList() ?? [],
      spokenLanguages: (json['spokenLanguages'] as List?)?.map((e) => e as String).toList() ?? [],
      filmingLocations: (json['filmingLocations'] as List?)?.map((e) => e as String).toList() ?? [],
      productionCompanies: (json['productionCompanies'] as List?)
              ?.map((e) => ProductionCompany.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      budget: json['budget'] as int? ?? 0,
      grossWorldwide: json['grossWorldwide'] as int? ?? 0,
      genres: (json['genres'] as List?)?.map((e) => e as String).toList() ?? [],
      isAdult: json['isAdult'] as bool? ?? false,
      runtimeMinutes: json['runtimeMinutes'] as int? ?? 0,
      averageRating: (json['averageRating'] as num?)?.toDouble() ?? 0.0,
      numVotes: json['numVotes'] as int? ?? 0,
      metascore: json['metascore'] as int? ?? 0,
      weekendGrossAmount: json['weekendGrossAmount'] as int? ?? 0,
      weekendGrossCurrency: json['weekendGrossCurrency'] as String? ?? 'USD',
      lifetimeGrossAmount: json['lifetimeGrossAmount'] as int? ?? 0,
      lifetimeGrossCurrency: json['lifetimeGrossCurrency'] as String? ?? 'USD',
      weeksRunning: json['weeksRunning'] as int? ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'url': url,
      'primaryTitle': primaryTitle,
      'originalTitle': originalTitle,
      'type': type,
      'description': description,
      'primaryImage': primaryImage,
      'thumbnails': thumbnails.map((e) => e.toJson()).toList(),
      'trailer': trailer,
      'contentRating': contentRating,
      'startYear': startYear,
      'endYear': endYear,
      'releaseDate': releaseDate,
      'interests': interests,
      'countriesOfOrigin': countriesOfOrigin,
      'externalLinks': externalLinks,
      'spokenLanguages': spokenLanguages,
      'filmingLocations': filmingLocations,
      'productionCompanies': productionCompanies.map((e) => e.toJson()).toList(),
      'budget': budget,
      'grossWorldwide': grossWorldwide,
      'genres': genres,
      'isAdult': isAdult,
      'runtimeMinutes': runtimeMinutes,
      'averageRating': averageRating,
      'numVotes': numVotes,
      'metascore': metascore,
      'weekendGrossAmount': weekendGrossAmount,
      'weekendGrossCurrency': weekendGrossCurrency,
      'lifetimeGrossAmount': lifetimeGrossAmount,
      'lifetimeGrossCurrency': lifetimeGrossCurrency,
      'weeksRunning': weeksRunning,
    };
  }
}

class MovieThumbnail {
  final String url;
  final int width;
  final int height;

  MovieThumbnail({
    required this.url,
    required this.width,
    required this.height,
  });

  factory MovieThumbnail.fromJson(Map<String, dynamic> json) {
    return MovieThumbnail(
      url: json['url'] as String? ?? '',
      width: json['width'] as int? ?? 0,
      height: json['height'] as int? ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'url': url,
      'width': width,
      'height': height,
    };
  }
}

class ProductionCompany {
  final String id;
  final String name;

  ProductionCompany({
    required this.id,
    required this.name,
  });

  factory ProductionCompany.fromJson(Map<String, dynamic> json) {
    return ProductionCompany(
      id: json['id'] as String? ?? '',
      name: json['name'] as String? ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
    };
  }
}
