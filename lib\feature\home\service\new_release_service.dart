import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:movie_proj/core/const.dart';
import 'package:movie_proj/feature/home/<USER>/new_release_model.dart';

class NewReleaseService {
  /// Fetches top box office movies from the IMDB API
  Future<List<NewReleaseMovie>> getTopBoxOffice() async {
    try {
      final url =
          Uri.parse('https://imdb236.p.rapidapi.com/api/imdb/top-box-office');

      final response = await http.get(
        url,
        headers: {
          'X-RapidAPI-Key': xRapidAPIKey,
          'X-RapidAPI-Host': xRapidAPIHost,
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);

        // The API response structure might be different, so we need to handle it properly
        if (data is List) {
          // If the response is directly a list of movies
          return _parseMoviesList(data);
        } else if (data is Map<String, dynamic>) {
          // If the response is wrapped in an object
          if (data.containsKey('results')) {
            return _parseMoviesList(data['results']);
          } else if (data.containsKey('data')) {
            return _parseMoviesList(data['data']);
          } else if (data.containsKey('movies')) {
            return _parseMoviesList(data['movies']);
          } else {
            // Try to find any list in the response
            for (var value in data.values) {
              if (value is List) {
                return _parseMoviesList(value);
              }
            }
          }
        }

        throw Exception('Unexpected API response structure');
      } else {
        throw Exception(
            'Failed to fetch box office movies: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('⚠️ Box Office API call failed, using mock data: $e');
      }
      // Return mock data as fallback
      return await getMockNewReleases();
    }
  }

  /// Parses a list of movie data from the API response
  List<NewReleaseMovie> _parseMoviesList(List<dynamic> moviesList) {
    // Limit to 5 movies for new releases section
    return moviesList.take(5).map((movieData) {
      try {
        return _parseMovieData(movieData as Map<String, dynamic>);
      } catch (e) {
        if (kDebugMode) {
          debugPrint('Error parsing movie data: $e');
          debugPrint('Movie data: $movieData');
        }
        // Return a placeholder movie if parsing fails
        return _createPlaceholderMovie(movieData);
      }
    }).toList();
  }

  /// Parses individual movie data with safe fallbacks
  NewReleaseMovie _parseMovieData(Map<String, dynamic> json) {
    return NewReleaseMovie(
      id: _safeString(json['id'] ?? json['imdbId'] ?? json['tconst'] ?? ''),
      url: _safeString(json['url'] ?? json['imdbUrl'] ?? ''),
      primaryTitle: _safeString(json['primaryTitle'] ??
          json['title'] ??
          json['titleText']?['text'] ??
          'Unknown Title'),
      originalTitle: _safeString(json['originalTitle'] ??
          json['title'] ??
          json['titleText']?['text'] ??
          'Unknown Title'),
      type: _safeString(json['type'] ?? json['titleType'] ?? 'movie'),
      description: _safeString(json['description'] ??
          json['plot'] ??
          json['plotText']?['plainText'] ??
          ''),
      primaryImage: _safeString(json['primaryImage'] ??
          json['image'] ??
          json['primaryImage']?['url'] ??
          ''),
      thumbnails: _parseThumbnails(json['thumbnails'] ?? json['images'] ?? []),
      trailer: _safeString(json['trailer'] ?? json['trailerUrl'] ?? ''),
      contentRating:
          _safeString(json['contentRating'] ?? json['certificate'] ?? ''),
      startYear: _safeInt(
          json['startYear'] ?? json['year'] ?? json['releaseYear'] ?? 0),
      endYear: json['endYear'] as int?,
      releaseDate: _safeString(json['releaseDate'] ?? json['released'] ?? ''),
      interests: _safeStringList(json['interests'] ?? json['keywords'] ?? []),
      countriesOfOrigin:
          _safeStringList(json['countriesOfOrigin'] ?? json['countries'] ?? []),
      externalLinks: _safeStringList(json['externalLinks'] ?? []),
      spokenLanguages:
          _safeStringList(json['spokenLanguages'] ?? json['languages'] ?? []),
      filmingLocations:
          _safeStringList(json['filmingLocations'] ?? json['locations'] ?? []),
      productionCompanies: _parseProductionCompanies(
          json['productionCompanies'] ?? json['companies'] ?? []),
      budget: _safeInt(json['budget'] ?? 0),
      grossWorldwide:
          _safeInt(json['grossWorldwide'] ?? json['boxOffice'] ?? 0),
      genres: _safeStringList(json['genres'] ?? json['genre'] ?? []),
      isAdult: json['isAdult'] as bool? ?? false,
      runtimeMinutes: _safeInt(
          json['runtimeMinutes'] ?? json['runtime'] ?? json['duration'] ?? 0),
      averageRating: _safeDouble(
          json['averageRating'] ?? json['rating'] ?? json['imdbRating'] ?? 0.0),
      numVotes:
          _safeInt(json['numVotes'] ?? json['votes'] ?? json['voteCount'] ?? 0),
      metascore: _safeInt(json['metascore'] ?? json['metacriticRating'] ?? 0),
      weekendGrossAmount: _safeInt(json['weekendGrossAmount'] ?? 0),
      weekendGrossCurrency: _safeString(json['weekendGrossCurrency'] ?? 'USD'),
      lifetimeGrossAmount: _safeInt(json['lifetimeGrossAmount'] ?? 0),
      lifetimeGrossCurrency:
          _safeString(json['lifetimeGrossCurrency'] ?? 'USD'),
      weeksRunning: _safeInt(json['weeksRunning'] ?? 0),
    );
  }

  /// Creates a placeholder movie when parsing fails
  NewReleaseMovie _createPlaceholderMovie(Map<String, dynamic> json) {
    return NewReleaseMovie(
      id: _safeString(json['id'] ?? 'unknown'),
      url: '',
      primaryTitle:
          _safeString(json['title'] ?? json['primaryTitle'] ?? 'Unknown Movie'),
      originalTitle:
          _safeString(json['title'] ?? json['primaryTitle'] ?? 'Unknown Movie'),
      type: 'movie',
      description: 'Description not available',
      primaryImage: _safeString(json['image'] ?? json['primaryImage'] ?? ''),
      thumbnails: [],
      trailer: '',
      contentRating: '',
      startYear: _safeInt(json['year'] ?? 2025),
      endYear: null,
      releaseDate: '',
      interests: [],
      countriesOfOrigin: [],
      externalLinks: [],
      spokenLanguages: [],
      filmingLocations: [],
      productionCompanies: [],
      budget: 0,
      grossWorldwide: 0,
      genres: [],
      isAdult: false,
      runtimeMinutes: 0,
      averageRating: _safeDouble(json['rating'] ?? 0.0),
      numVotes: 0,
      metascore: 0,
      weekendGrossAmount: 0,
      weekendGrossCurrency: 'USD',
      lifetimeGrossAmount: 0,
      lifetimeGrossCurrency: 'USD',
      weeksRunning: 0,
    );
  }

  /// Safe string parsing with fallback
  String _safeString(dynamic value) {
    if (value == null) return '';
    if (value is String) return value;
    return value.toString();
  }

  /// Safe integer parsing with fallback
  int _safeInt(dynamic value) {
    if (value == null) return 0;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) {
      return int.tryParse(value) ?? 0;
    }
    return 0;
  }

  /// Safe double parsing with fallback
  double _safeDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value) ?? 0.0;
    }
    return 0.0;
  }

  /// Safe string list parsing with fallback
  List<String> _safeStringList(dynamic value) {
    if (value == null) return [];
    if (value is List) {
      return value.map((e) => _safeString(e)).toList();
    }
    if (value is String) {
      return [value];
    }
    return [];
  }

  /// Parse thumbnails with safe fallbacks
  List<MovieThumbnail> _parseThumbnails(dynamic thumbnails) {
    if (thumbnails == null || thumbnails is! List) return [];

    return thumbnails.map((thumb) {
      if (thumb is Map<String, dynamic>) {
        return MovieThumbnail(
          url: _safeString(thumb['url'] ?? ''),
          width: _safeInt(thumb['width'] ?? 0),
          height: _safeInt(thumb['height'] ?? 0),
        );
      }
      return MovieThumbnail(url: '', width: 0, height: 0);
    }).toList();
  }

  /// Parse production companies with safe fallbacks
  List<ProductionCompany> _parseProductionCompanies(dynamic companies) {
    if (companies == null || companies is! List) return [];

    return companies.map((company) {
      if (company is Map<String, dynamic>) {
        return ProductionCompany(
          id: _safeString(company['id'] ?? ''),
          name: _safeString(company['name'] ?? ''),
        );
      }
      return ProductionCompany(id: '', name: '');
    }).toList();
  }

  /// Fallback method that returns mock new release data (limited to 5 movies)
  Future<List<NewReleaseMovie>> getMockNewReleases() async {
    return [
      NewReleaseMovie(
        id: 'tt10548174',
        url: 'https://www.imdb.com/title/tt10548174/',
        primaryTitle: '28 Years Later',
        originalTitle: '28 Years Later',
        type: 'movie',
        description:
            'A group of survivors of the rage virus live on a small island. When one of the group leaves the island on a mission into the mainland, he discovers secrets, wonders, and horrors that have mutated not only the infected but other survivors.',
        primaryImage:
            'https://m.media-amazon.com/images/M/MV5BNjgwYTI0YjctMWYzNS00MmI1LWI5YTctNmE1YjBkNDFlNWMxXkEyXkFqcGc@.jpg',
        thumbnails: [
          MovieThumbnail(
            url:
                'https://m.media-amazon.com/images/M/MV5BNjgwYTI0YjctMWYzNS00MmI1LWI5YTctNmE1YjBkNDFlNWMxXkEyXkFqcGc@._V1_QL75_UX100_CR0,0,100,148_.jpg',
            width: 100,
            height: 148,
          ),
          MovieThumbnail(
            url:
                'https://m.media-amazon.com/images/M/MV5BNjgwYTI0YjctMWYzNS00MmI1LWI5YTctNmE1YjBkNDFlNWMxXkEyXkFqcGc@._V1_QL75_UX280_CR0,0,280,414_.jpg',
            width: 280,
            height: 414,
          ),
        ],
        trailer: 'https://www.youtube.com/watch?v=mcvLKldPM08',
        contentRating: 'R',
        startYear: 2025,
        endYear: null,
        releaseDate: '2025-06-20',
        interests: ['Zombie Horror', 'Horror', 'Thriller'],
        countriesOfOrigin: ['GB', 'US'],
        externalLinks: ['https://28yearslater.movie/'],
        spokenLanguages: ['en'],
        filmingLocations: [
          'Holy Island of Lindisfarne, Berwick-upon-Tweed, Northumberland, England, UK'
        ],
        productionCompanies: [
          ProductionCompany(id: 'co0420822', name: 'TSG Entertainment'),
          ProductionCompany(id: 'co1134642', name: 'Danny Boyle'),
          ProductionCompany(
              id: 'co0037450', name: 'British Film Institute (BFI)'),
        ],
        budget: 60000000,
        grossWorldwide: *********,
        genres: ['Horror', 'Thriller'],
        isAdult: false,
        runtimeMinutes: 115,
        averageRating: 7.1,
        numVotes: 57164,
        metascore: 77,
        weekendGrossAmount: 4567059,
        weekendGrossCurrency: 'USD',
        lifetimeGrossAmount: 60902114,
        lifetimeGrossCurrency: 'USD',
        weeksRunning: 3,
      ),
    ];
  }
}
