import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:movie_proj/feature/home/<USER>/new_release_model.dart';
import 'package:movie_proj/feature/home/<USER>/new_release_service.dart';

// States
abstract class NewReleaseState {}

class NewReleaseInitial extends NewReleaseState {}

class NewReleaseLoading extends NewReleaseState {}

class NewReleaseLoaded extends NewReleaseState {
  final List<NewReleaseMovie> movies;

  NewReleaseLoaded(this.movies);
}

class NewReleaseError extends NewReleaseState {
  final String message;

  NewReleaseError(this.message);
}

// Cubit
class NewReleaseCubit extends Cubit<NewReleaseState> {
  final NewReleaseService _newReleaseService;

  NewReleaseCubit(this._newReleaseService) : super(NewReleaseInitial());

  Future<void> loadNewReleases() async {
    try {
      emit(NewReleaseLoading());
      final movies = await _newReleaseService.getTopBoxOffice();
      emit(NewReleaseLoaded(movies));
    } catch (e) {
      emit(NewReleaseError('Failed to load new releases: $e'));
    }
  }

  Future<void> refreshNewReleases() async {
    await loadNewReleases();
  }
}
