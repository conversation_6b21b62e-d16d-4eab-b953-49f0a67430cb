import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:movie_proj/feature/home/<USER>/popular_tv_model.dart';
import 'package:movie_proj/feature/home/<USER>/popular_tv_service.dart';

// States
abstract class PopularTVState {}

class PopularTVInitial extends PopularTVState {}

class PopularTVLoading extends PopularTVState {}

class PopularTVLoaded extends PopularTVState {
  final List<PopularTVShow> tvShows;

  PopularTVLoaded(this.tvShows);
}

class PopularTVError extends PopularTVState {
  final String message;

  PopularTVError(this.message);
}

// Cubit
class PopularTVCubit extends Cubit<PopularTVState> {
  final PopularTVService _popularTVService;

  PopularTVCubit(this._popularTVService) : super(PopularTVInitial());

  Future<void> loadPopularTV() async {
    try {
      emit(PopularTVLoading());
      final tvShows = await _popularTVService.getMostPopularTV();
      emit(PopularTVLoaded(tvShows));
    } catch (e) {
      emit(PopularTVError('Failed to load popular TV shows: $e'));
    }
  }

  Future<void> refreshPopularTV() async {
    await loadPopularTV();
  }
}
