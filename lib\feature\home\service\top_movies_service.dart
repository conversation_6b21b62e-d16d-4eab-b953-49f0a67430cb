import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:movie_proj/feature/home/<USER>/top_moves.dart';

class TopMoviesService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  Future<List<TopMovie>> getTopMovies() async {
    try {
      final QuerySnapshot querySnapshot = await _firestore
          .collection('top_movies')
          .orderBy('averageRating', descending: true)
          .limit(10)
          .get();

      return querySnapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return TopMovie.fromJson(data);
      }).toList();
    } catch (e) {
      print('Error fetching top movies: $e');
      return [];
    }
  }

  Future<void> addTopMovie(TopMovie movie) async {
    try {
      await _firestore.collection('top_movies').doc(movie.id).set({
        'id': movie.id,
        'url': movie.url,
        'primaryTitle': movie.primaryTitle,
        'originalTitle': movie.originalTitle,
        'type': movie.type,
        'description': movie.description,
        'primaryImage': movie.primaryImage,
        'thumbnails': movie.thumbnails
            .map((thumbnail) => {
                  'url': thumbnail.url,
                  'width': thumbnail.width,
                  'height': thumbnail.height,
                })
            .toList(),
        'trailer': movie.trailer,
        'contentRating': movie.contentRating,
        'startYear': movie.startYear,
        'endYear': movie.endYear,
        'releaseDate': movie.releaseDate,
        'interests': movie.interests,
        'countriesOfOrigin': movie.countriesOfOrigin,
        'externalLinks': movie.externalLinks,
        'spokenLanguages': movie.spokenLanguages,
        'filmingLocations': movie.filmingLocations,
        'productionCompanies': movie.productionCompanies
            .map((company) => {
                  'id': company.id,
                  'name': company.name,
                })
            .toList(),
        'budget': movie.budget,
        'grossWorldwide': movie.grossWorldwide,
        'genres': movie.genres,
        'isAdult': movie.isAdult,
        'runtimeMinutes': movie.runtimeMinutes,
        'averageRating': movie.averageRating,
        'numVotes': movie.numVotes,
        'metascore': movie.metascore,
      });
    } catch (e) {
      print('Error adding top movie: $e');
      throw e;
    }
  }
}
