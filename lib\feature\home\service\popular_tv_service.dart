import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:movie_proj/core/const.dart';
import 'package:movie_proj/feature/home/<USER>/popular_tv_model.dart';

class PopularTVService {
  /// Fetches most popular TV shows from the IMDB API
  Future<List<PopularTVShow>> getMostPopularTV() async {
    try {
      final url =
          Uri.parse('https://imdb236.p.rapidapi.com/api/imdb/most-popular-tv');

      final response = await http.get(
        url,
        headers: {
          'X-RapidAPI-Key': xRapidAPIKey,
          'X-RapidAPI-Host': xRapidAPIHost,
        },
      );

      if (response.statusCode == 200) {
        final dynamic responseData = json.decode(response.body);

        if (kDebugMode) {
          debugPrint('✅ Popular TV API Response received');
          debugPrint('📊 Response type: ${responseData.runtimeType}');
          if (responseData is Map) {
            debugPrint('📊 Response keys: ${responseData.keys.toList()}');
          }
        }

        // Handle different possible response structures
        List<dynamic> tvShowsList = [];

        if (responseData is List) {
          // Direct list response
          tvShowsList = responseData;
        } else if (responseData is Map<String, dynamic>) {
          final data = responseData;
          // Try different possible keys for the TV shows list
          if (data.containsKey('data') && data['data'] is List) {
            tvShowsList = data['data'] as List<dynamic>;
          } else if (data.containsKey('results') && data['results'] is List) {
            tvShowsList = data['results'] as List<dynamic>;
          } else if (data.containsKey('items') && data['items'] is List) {
            tvShowsList = data['items'] as List<dynamic>;
          } else if (data.containsKey('shows') && data['shows'] is List) {
            tvShowsList = data['shows'] as List<dynamic>;
          } else if (data.containsKey('tvShows') && data['tvShows'] is List) {
            tvShowsList = data['tvShows'] as List<dynamic>;
          } else {
            // If no known key found, try to find any list in the response
            for (var value in data.values) {
              if (value is List && value.isNotEmpty) {
                tvShowsList = value;
                break;
              }
            }
          }
        }

        if (tvShowsList.isNotEmpty) {
          if (kDebugMode) {
            debugPrint(
                '📺 Found ${tvShowsList.length} TV shows in API response');
          }
          return _parseTVShowsList(tvShowsList);
        }

        if (kDebugMode) {
          debugPrint('⚠️ No TV shows found in response, using mock data');
          debugPrint(
              '📊 Full response: ${responseData.toString().substring(0, 200)}...');
        }

        // Return mock data if no shows found
        return await getMockPopularTV();
      } else {
        throw Exception(
            'Failed to fetch popular TV shows: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('⚠️ Popular TV API call failed, using mock data: $e');
      }
      // Return mock data as fallback
      return await getMockPopularTV();
    }
  }

  /// Parses a list of TV show data from the API response
  List<PopularTVShow> _parseTVShowsList(List<dynamic> tvShowsList) {
    // Limit to 5 TV shows for popular TV section
    return tvShowsList.take(5).map((tvShowData) {
      try {
        return _parseTVShowData(tvShowData as Map<String, dynamic>);
      } catch (e) {
        if (kDebugMode) {
          debugPrint('Error parsing TV show data: $e');
          debugPrint('TV show data: $tvShowData');
        }
        // Return a placeholder TV show if parsing fails
        return _createPlaceholderTVShow(tvShowData);
      }
    }).toList();
  }

  /// Parses individual TV show data with safe fallbacks
  PopularTVShow _parseTVShowData(Map<String, dynamic> json) {
    return PopularTVShow(
      id: _safeString(json['id'] ?? json['imdbId'] ?? json['tconst'] ?? ''),
      url: _safeString(json['url'] ?? json['imdbUrl'] ?? ''),
      primaryTitle: _safeString(json['primaryTitle'] ??
          json['title'] ??
          json['titleText']?['text'] ??
          'Unknown Title'),
      originalTitle: _safeString(json['originalTitle'] ??
          json['title'] ??
          json['titleText']?['text'] ??
          'Unknown Title'),
      type: _safeString(json['type'] ?? json['titleType'] ?? 'tvSeries'),
      description: _safeString(json['description'] ??
          json['plot'] ??
          json['plotText']?['plainText'] ??
          ''),
      primaryImage: _safeString(json['primaryImage'] ??
          json['image'] ??
          json['primaryImage']?['url'] ??
          ''),
      thumbnails: _parseThumbnails(json['thumbnails'] ?? json['images'] ?? []),
      trailer: _safeString(json['trailer'] ?? json['trailerUrl'] ?? ''),
      contentRating:
          _safeString(json['contentRating'] ?? json['certificate'] ?? ''),
      startYear: _safeInt(
          json['startYear'] ?? json['year'] ?? json['releaseYear'] ?? 0),
      endYear: json['endYear'] as int?,
      releaseDate: _safeString(json['releaseDate'] ?? json['released'] ?? ''),
      interests: _safeStringList(json['interests'] ?? json['keywords'] ?? []),
      countriesOfOrigin:
          _safeStringList(json['countriesOfOrigin'] ?? json['countries'] ?? []),
      externalLinks: _safeStringList(json['externalLinks'] ?? []),
      spokenLanguages:
          _safeStringList(json['spokenLanguages'] ?? json['languages'] ?? []),
      filmingLocations:
          _safeStringList(json['filmingLocations'] ?? json['locations'] ?? []),
      productionCompanies: _parseProductionCompanies(
          json['productionCompanies'] ?? json['companies'] ?? []),
      budget: _safeInt(json['budget'] ?? 0),
      grossWorldwide: _safeInt(json['grossWorldwide'] ?? json['gross'] ?? 0),
      genres: _safeStringList(json['genres'] ?? []),
      isAdult: json['isAdult'] as bool? ?? false,
      runtimeMinutes: _safeInt(json['runtimeMinutes'] ?? json['runtime'] ?? 0),
      averageRating:
          _safeDouble(json['averageRating'] ?? json['rating'] ?? 0.0),
      numVotes: _safeInt(json['numVotes'] ?? json['votes'] ?? 0),
      metascore: _safeInt(json['metascore'] ?? 0),
      numberOfSeasons:
          _safeInt(json['numberOfSeasons'] ?? json['seasons'] ?? 0),
      numberOfEpisodes:
          _safeInt(json['numberOfEpisodes'] ?? json['episodes'] ?? 0),
      status: _safeString(json['status'] ?? json['seriesStatus'] ?? 'unknown'),
    );
  }

  /// Creates a placeholder TV show when parsing fails
  PopularTVShow _createPlaceholderTVShow(Map<String, dynamic> json) {
    return PopularTVShow(
      id: _safeString(json['id'] ?? 'unknown'),
      url: '',
      primaryTitle: _safeString(
          json['title'] ?? json['primaryTitle'] ?? 'Unknown TV Show'),
      originalTitle: _safeString(
          json['title'] ?? json['primaryTitle'] ?? 'Unknown TV Show'),
      type: 'tvSeries',
      description: 'Description not available',
      primaryImage: _safeString(json['image'] ?? json['primaryImage'] ?? ''),
      thumbnails: [],
      trailer: '',
      contentRating: '',
      startYear: _safeInt(json['year'] ?? 2025),
      endYear: null,
      releaseDate: '',
      interests: [],
      countriesOfOrigin: [],
      externalLinks: [],
      spokenLanguages: [],
      filmingLocations: [],
      productionCompanies: [],
      budget: 0,
      grossWorldwide: 0,
      genres: [],
      isAdult: false,
      runtimeMinutes: 0,
      averageRating: _safeDouble(json['rating'] ?? 0.0),
      numVotes: 0,
      metascore: 0,
      numberOfSeasons: 0,
      numberOfEpisodes: 0,
      status: 'unknown',
    );
  }

  /// Safe string parsing with fallback
  String _safeString(dynamic value) {
    if (value == null) return '';
    if (value is String) return value;
    return value.toString();
  }

  /// Safe integer parsing with fallback
  int _safeInt(dynamic value) {
    if (value == null) return 0;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) {
      return int.tryParse(value) ?? 0;
    }
    return 0;
  }

  /// Safe double parsing with fallback
  double _safeDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value) ?? 0.0;
    }
    return 0.0;
  }

  /// Safe string list parsing with fallback
  List<String> _safeStringList(dynamic value) {
    if (value == null) return [];
    if (value is List) {
      return value.map((e) => e.toString()).toList();
    }
    return [];
  }

  /// Parses thumbnails with safe fallbacks
  List<TVThumbnail> _parseThumbnails(dynamic thumbnails) {
    if (thumbnails == null || thumbnails is! List) return [];

    return thumbnails.map((thumb) {
      if (thumb is Map<String, dynamic>) {
        return TVThumbnail(
          url: _safeString(thumb['url'] ?? ''),
          width: _safeInt(thumb['width'] ?? 0),
          height: _safeInt(thumb['height'] ?? 0),
        );
      }
      return TVThumbnail(url: '', width: 0, height: 0);
    }).toList();
  }

  /// Parses production companies with safe fallbacks
  List<ProductionCompany> _parseProductionCompanies(dynamic companies) {
    if (companies == null || companies is! List) return [];

    return companies.map((company) {
      if (company is Map<String, dynamic>) {
        return ProductionCompany(
          id: _safeString(company['id'] ?? ''),
          name: _safeString(company['name'] ?? ''),
        );
      }
      return ProductionCompany(id: '', name: '');
    }).toList();
  }

  /// Fallback method that returns mock popular TV data (limited to 5 shows)
  Future<List<PopularTVShow>> getMockPopularTV() async {
    return [
      PopularTVShow(
        id: 'tt0944947',
        url: 'https://www.imdb.com/title/tt0944947/',
        primaryTitle: 'Game of Thrones',
        originalTitle: 'Game of Thrones',
        type: 'tvSeries',
        description:
            'Nine noble families fight for control over the lands of Westeros, while an ancient enemy returns after being dormant for millennia.',
        primaryImage:
            'https://m.media-amazon.com/images/M/MV5BYTRiNDQwYzAtMzVlZS00NTI5LWJjYjUtMzkwNTUzMWMxZTllXkEyXkFqcGdeQXVyNDIzMzcwNjc@._V1_.jpg',
        thumbnails: [],
        trailer: 'https://www.youtube.com/watch?v=rlR4PJn8b8I',
        contentRating: 'TV-MA',
        startYear: 2011,
        endYear: 2019,
        releaseDate: '2011-04-17',
        interests: ['Fantasy', 'Drama', 'Adventure'],
        countriesOfOrigin: ['US', 'GB'],
        externalLinks: [],
        spokenLanguages: ['en'],
        filmingLocations: ['Northern Ireland', 'Croatia', 'Spain'],
        productionCompanies: [
          ProductionCompany(id: 'co0047120', name: 'HBO'),
          ProductionCompany(id: 'co0159111', name: 'Television 360'),
        ],
        budget: 0,
        grossWorldwide: 0,
        genres: ['Action', 'Adventure', 'Drama'],
        isAdult: false,
        runtimeMinutes: 57,
        averageRating: 9.2,
        numVotes: 2000000,
        metascore: 90,
        numberOfSeasons: 8,
        numberOfEpisodes: 73,
        status: 'ended',
      ),
      PopularTVShow(
        id: 'tt0903747',
        url: 'https://www.imdb.com/title/tt0903747/',
        primaryTitle: 'Breaking Bad',
        originalTitle: 'Breaking Bad',
        type: 'tvSeries',
        description:
            'A chemistry teacher diagnosed with inoperable lung cancer turns to manufacturing and selling methamphetamine with a former student in order to secure his family\'s future.',
        primaryImage:
            'https://m.media-amazon.com/images/M/MV5BMjhiMzgxZTctNDc1Ni00OTIxLTlhMTYtZTA3ZWFkODRkNmFmXkEyXkFqcGdeQXVyNzkwMjQ5NzM@._V1_.jpg',
        thumbnails: [],
        trailer: 'https://www.youtube.com/watch?v=HhesaQXLuRY',
        contentRating: 'TV-MA',
        startYear: 2008,
        endYear: 2013,
        releaseDate: '2008-01-20',
        interests: ['Crime', 'Drama', 'Thriller'],
        countriesOfOrigin: ['US'],
        externalLinks: [],
        spokenLanguages: ['en'],
        filmingLocations: ['Albuquerque, New Mexico, USA'],
        productionCompanies: [
          ProductionCompany(id: 'co0047120', name: 'AMC'),
          ProductionCompany(id: 'co0319272', name: 'High Bridge Entertainment'),
        ],
        budget: 0,
        grossWorldwide: 0,
        genres: ['Crime', 'Drama', 'Thriller'],
        isAdult: false,
        runtimeMinutes: 47,
        averageRating: 9.5,
        numVotes: 1800000,
        metascore: 99,
        numberOfSeasons: 5,
        numberOfEpisodes: 62,
        status: 'ended',
      ),
      PopularTVShow(
        id: 'tt0108778',
        url: 'https://www.imdb.com/title/tt0108778/',
        primaryTitle: 'Friends',
        originalTitle: 'Friends',
        type: 'tvSeries',
        description:
            'Follows the personal and professional lives of six twenty to thirty year-old friends living in the Manhattan borough of New York City.',
        primaryImage:
            'https://m.media-amazon.com/images/M/MV5BNDVkYjU0MzctMWRmZi00NTkxLTgwZWEtOWVhYjZlYjllYmU4XkEyXkFqcGdeQXVyNTA4NzY1MzY@._V1_.jpg',
        thumbnails: [],
        trailer: 'https://www.youtube.com/watch?v=hDNNmeeJs1Q',
        contentRating: 'TV-PG-13',
        startYear: 1994,
        endYear: 2004,
        releaseDate: '1994-09-22',
        interests: ['Comedy', 'Romance'],
        countriesOfOrigin: ['US'],
        externalLinks: [],
        spokenLanguages: ['en'],
        filmingLocations: ['New York City, New York, USA'],
        productionCompanies: [
          ProductionCompany(id: 'co0047259', name: 'NBC'),
          ProductionCompany(id: 'co0002663', name: 'Warner Bros. Television'),
        ],
        budget: 0,
        grossWorldwide: 0,
        genres: ['Comedy', 'Romance'],
        isAdult: false,
        runtimeMinutes: 22,
        averageRating: 8.9,
        numVotes: 1000000,
        metascore: 79,
        numberOfSeasons: 10,
        numberOfEpisodes: 236,
        status: 'ended',
      ),
    ];
  }
}
