import 'package:flutter/foundation.dart';
import 'package:movie_proj/feature/home/<USER>/popular_tv_service.dart';
import 'package:movie_proj/core/service/service.dart';

/// Test function to verify Popular TV API connection and response structure
Future<void> testPopularTVAPI() async {
  try {
    debugPrint('📺 Testing Popular TV API...');
    
    // Test using the service directly
    final service = PopularTVService();
    final tvShows = await service.getMostPopularTV();
    
    debugPrint('✅ API call successful!');
    debugPrint('📊 Number of popular TV shows fetched: ${tvShows.length}');
    
    if (tvShows.isNotEmpty) {
      final firstShow = tvShows.first;
      debugPrint('🎬 First TV show details:');
      debugPrint('   ID: ${firstShow.id}');
      debugPrint('   Title: ${firstShow.primaryTitle}');
      debugPrint('   Type: ${firstShow.type}');
      debugPrint('   Rating: ${firstShow.averageRating}');
      debugPrint('   Seasons: ${firstShow.numberOfSeasons}');
      debugPrint('   Episodes: ${firstShow.numberOfEpisodes}');
      debugPrint('   Status: ${firstShow.status}');
      debugPrint('   Genres: ${firstShow.genres.join(', ')}');
      debugPrint('   Start Year: ${firstShow.startYear}');
      debugPrint('   End Year: ${firstShow.endYear ?? 'Ongoing'}');
      debugPrint('   Content Rating: ${firstShow.contentRating}');
      debugPrint('   Description: ${firstShow.description.length > 100 ? firstShow.description.substring(0, 100) + '...' : firstShow.description}');
    }
    
    // Test using the convenience function
    debugPrint('\n🔄 Testing convenience function...');
    final tvShowsFromFunction = await mostPopularTV();
    debugPrint('✅ Convenience function successful!');
    debugPrint('📊 Number of TV shows from function: ${tvShowsFromFunction.length}');
    
  } catch (e) {
    debugPrint('❌ Popular TV API test failed: $e');
  }
}

/// Test function to verify mock data works correctly
Future<void> testPopularTVMockData() async {
  try {
    debugPrint('🎭 Testing Popular TV Mock Data...');
    
    final service = PopularTVService();
    final mockTVShows = await service.getMockPopularTV();
    
    debugPrint('✅ Mock data loaded successfully!');
    debugPrint('📊 Number of mock TV shows: ${mockTVShows.length}');
    
    for (int i = 0; i < mockTVShows.length; i++) {
      final show = mockTVShows[i];
      debugPrint('🎬 Mock TV Show ${i + 1}:');
      debugPrint('   Title: ${show.primaryTitle}');
      debugPrint('   Rating: ${show.averageRating}');
      debugPrint('   Seasons: ${show.numberOfSeasons}');
      debugPrint('   Episodes: ${show.numberOfEpisodes}');
      debugPrint('   Status: ${show.status}');
      debugPrint('   Years: ${show.startYear}-${show.endYear ?? 'Present'}');
    }
    
  } catch (e) {
    debugPrint('❌ Popular TV Mock data test failed: $e');
  }
}

/// Comprehensive test function that runs all Popular TV tests
Future<void> runAllPopularTVTests() async {
  debugPrint('🚀 Starting Popular TV Service Tests...\n');
  
  await testPopularTVAPI();
  debugPrint('\n' + '='*50 + '\n');
  await testPopularTVMockData();
  
  debugPrint('\n🏁 Popular TV Service Tests Completed!');
}
