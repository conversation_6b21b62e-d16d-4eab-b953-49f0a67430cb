import 'package:flutter/material.dart';
import 'package:movie_proj/core/my_images.dart';
import 'package:movie_proj/core/my_styles.dart';
import 'package:movie_proj/core/my_text.dart';
import 'package:movie_proj/core/spacing.dart';
import 'package:movie_proj/feature/auth/widget/my_text_btns.dart';
import 'package:movie_proj/feature/auth/widget/social_btn.dart';

class HomeMostWatched extends StatelessWidget {
  const HomeMostWatched({super.key});

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final contentWidth = screenWidth > 600 ? 350.0 : screenWidth * 0.8;
    final titleFontSize = screenWidth > 600 ? 48.0 : 32.0;
    final buttonWidth = screenWidth > 600 ? 120.0 : 100.0;

    return Stack(
      children: [
        Image.asset(
          MyImages.background,
          width: double.infinity,
          height: screenWidth > 600 ? 500 : 400,
          fit: BoxFit.cover,
        ),
        Positioned(
          top: screenWidth > 600 ? 150 : 100,
          left: screenWidth > 600 ? 30 : 20,
          child: Container(
            width: contentWidth,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  MyText.johnWick,
                  style: MyStyles.title24White700
                      .copyWith(fontSize: titleFontSize),
                ),
                vSpace(15),
                Text(
                  MyText.johnWickCaption,
                  style: MyStyles.title24White400.copyWith(fontSize: 14),
                ),
                vSpace(20),
                Row(
                  children: [
                    Image.asset(MyImages.imdb, width: 50),
                    hSpace(10),
                    Text(
                      '86.0/100',
                      style: MyStyles.title24White400.copyWith(fontSize: 14),
                    ),
                  ],
                ),
                vSpace(20),
                Wrap(
                  spacing: 10,
                  runSpacing: 10,
                  children: [
                    MyTextBtn(
                      onTap: () {},
                      text: MyText.addToPlaylist,
                      color: Colors.white,
                      textColor: Colors.black,
                      radius: 5,
                      width: buttonWidth,
                    ),
                    SocialBtn(
                      color: const Color(0xff6D6D6E),
                      textColor: Colors.white,
                      imagePath: MyImages.iconError,
                      radius: 5,
                      width: buttonWidth,
                      text: MyText.moreInfo,
                    )
                  ],
                )
              ],
            ),
          ),
        )
      ],
    );
  }
}
