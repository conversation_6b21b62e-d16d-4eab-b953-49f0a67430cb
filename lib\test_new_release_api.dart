import 'package:flutter/foundation.dart';
import 'package:movie_proj/feature/home/<USER>/new_release_service.dart';
import 'package:movie_proj/core/service/service.dart';

/// Test function to verify New Release API connection and response structure
Future<void> testNewReleaseAPI() async {
  try {
    debugPrint('🎬 Testing New Release (Top Box Office) API...');
    
    // Test using the service directly
    final service = NewReleaseService();
    final movies = await service.getTopBoxOffice();
    
    debugPrint('✅ API call successful!');
    debugPrint('📊 Number of new release movies fetched: ${movies.length}');
    
    if (movies.isNotEmpty) {
      final firstMovie = movies.first;
      debugPrint('🎭 First new release movie details:');
      debugPrint('   Title: ${firstMovie.primaryTitle}');
      debugPrint('   Year: ${firstMovie.startYear}');
      debugPrint('   Rating: ${firstMovie.averageRating}');
      debugPrint('   Image: ${firstMovie.primaryImage}');
      debugPrint('   Genres: ${firstMovie.genres.join(', ')}');
      debugPrint('   Box Office: \$${firstMovie.lifetimeGrossAmount} ${firstMovie.lifetimeGrossCurrency}');
      debugPrint('   Weekend Gross: \$${firstMovie.weekendGrossAmount} ${firstMovie.weekendGrossCurrency}');
      debugPrint('   Weeks Running: ${firstMovie.weeksRunning}');
    }
    
    // Test using the convenience function
    debugPrint('\n🔄 Testing convenience function...');
    final moviesFromFunction = await topBoxOffice();
    debugPrint('✅ Convenience function successful!');
    debugPrint('📊 Number of movies from function: ${moviesFromFunction.length}');
    
  } catch (e) {
    debugPrint('❌ New Release API test failed: $e');
  }
}

/// Test function to verify mock data works correctly
Future<void> testNewReleaseMockData() async {
  try {
    debugPrint('🎭 Testing New Release Mock Data...');
    
    final service = NewReleaseService();
    final mockMovies = await service.getMockNewReleases();
    
    debugPrint('✅ Mock data loaded successfully!');
    debugPrint('📊 Number of mock movies: ${mockMovies.length}');
    
    if (mockMovies.isNotEmpty) {
      final movie = mockMovies.first;
      debugPrint('🎬 Mock movie details:');
      debugPrint('   Title: ${movie.primaryTitle}');
      debugPrint('   Year: ${movie.startYear}');
      debugPrint('   Rating: ${movie.averageRating}');
      debugPrint('   Box Office: \$${movie.lifetimeGrossAmount} ${movie.lifetimeGrossCurrency}');
      debugPrint('   Genres: ${movie.genres.join(', ')}');
      debugPrint('   Description: ${movie.description}');
    }
    
  } catch (e) {
    debugPrint('❌ Mock data test failed: $e');
  }
}
