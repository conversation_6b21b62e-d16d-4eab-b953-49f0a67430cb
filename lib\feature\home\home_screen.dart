import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:movie_proj/core/my_colors.dart';
import 'package:movie_proj/core/my_styles.dart';
import 'package:movie_proj/core/my_text.dart';
import 'package:movie_proj/core/spacing.dart';
import 'package:movie_proj/core/widget/movie_app_bar.dart';
import 'package:movie_proj/feature/home/<USER>/top_movies_cubit.dart';
import 'package:movie_proj/feature/home/<USER>/top_moves.dart';
import 'package:movie_proj/feature/home/<USER>/film_avatars.dart';
import 'package:movie_proj/feature/home/<USER>/film_info_widget.dart';
import 'package:movie_proj/feature/home/<USER>/film_poster.dart';
import 'package:movie_proj/feature/home/<USER>/home_most_watched.dart';

class HomeScreen extends StatefulWidget {
  final Function(int) onNavigate;

  const HomeScreen({
    super.key,
    required this.onNavigate,
  });

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  void initState() {
    super.initState();
    // Load movies when the screen is first mounted
    Future.microtask(() => context.read<TopMoviesCubit>().loadTopMovies());
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final horizontalPadding = screenWidth > 600 ? 60.0 : 20.0;

    return Scaffold(
      backgroundColor: MyColors.primaryColor,
      appBar: MovieAppBar(
        currentIndex: 0,
        onNavigate: widget.onNavigate,
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            const Padding(
              padding: EdgeInsets.symmetric(vertical: 20),
              child: Text(
                MyText.mostWatched,
                style: MyStyles.title24White400,
              ),
            ),
            const HomeMostWatched(),
            vSpace(30),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(MyText.tvShows,
                      style: MyStyles.title24White400.copyWith(fontSize: 14)),
                  hSpace(40),
                  Text(MyText.movies,
                      style: MyStyles.title24White400.copyWith(fontSize: 14)),
                  hSpace(40),
                  Text(MyText.newPopular,
                      style: MyStyles.title24White400.copyWith(fontSize: 14)),
                  hSpace(40),
                  Text(MyText.myList,
                      style: MyStyles.title24White400.copyWith(fontSize: 14)),
                  hSpace(40),
                  Text(MyText.browseByLanguage,
                      style: MyStyles.title24White400.copyWith(fontSize: 14)),
                ],
              ),
            ),
            vSpace(30),
            Center(
              child: Text(
                'top Movies',
                style: MyStyles.title24White400.copyWith(
                  fontSize: screenWidth > 600 ? 24.0 : 20.0,
                ),
              ),
            ),
            vSpace(30),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
              child: BlocBuilder<TopMoviesCubit, TopMoviesState>(
                builder: (context, state) {
                  if (state is TopMoviesLoading) {
                    return _buildLoadingState(screenWidth);
                  } else if (state is TopMoviesError) {
                    return _buildErrorState(state.message, context);
                  } else if (state is TopMoviesLoaded) {
                    final movies = state.movies;
                    if (movies.isEmpty) {
                      return _buildEmptyState();
                    }
                    return _buildMoviesLayout(movies, screenWidth);
                  }
                  return const SizedBox.shrink();
                },
              ),
            ),
            vSpace(50),
            const FilmInfoWidget(
              text: MyText.newRelease,
            ),
            vSpace(30),
            const FilmInfoWidget(text: MyText.pickedForYou),
            vSpace(50),
            const FilmAvatars(),
            vSpace(30),
            dSpace(),
            vSpace(20)
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingState(double screenWidth) {
    return SizedBox(
      height: 300,
      child: screenWidth > 900
          ? Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: List.generate(
                5,
                (index) => _buildLoadingSkeleton(),
              ),
            )
          : SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: List.generate(
                  5,
                  (index) => Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 10),
                    child: _buildLoadingSkeleton(),
                  ),
                ),
              ),
            ),
    );
  }

  Widget _buildLoadingSkeleton() {
    return Container(
      width: 200,
      height: 300,
      decoration: BoxDecoration(
        color: Colors.grey[800],
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Center(
        child: CircularProgressIndicator(
          color: Colors.white,
          strokeWidth: 2,
        ),
      ),
    );
  }

  Widget _buildErrorState(String message, BuildContext context) {
    return Container(
      height: 300,
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            color: Colors.red,
            size: 48,
          ),
          const SizedBox(height: 16),
          Text(
            'Failed to load movies',
            style: MyStyles.title24White400.copyWith(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: MyStyles.title24White400.copyWith(
              color: Colors.red,
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              context.read<TopMoviesCubit>().loadTopMovies();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: MyColors.btnColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      height: 300,
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.movie_outlined,
            color: Colors.grey[600],
            size: 48,
          ),
          const SizedBox(height: 16),
          Text(
            'No movies available',
            style: MyStyles.title24White400.copyWith(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'Check back later for new releases',
            style: MyStyles.title24White400.copyWith(
              color: Colors.grey[600],
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildMoviesLayout(List<TopMovie> movies, double screenWidth) {
    if (screenWidth > 900) {
      // Desktop layout - show movies in a responsive grid
      return _buildDesktopLayout(movies);
    } else {
      // Mobile/Tablet layout - horizontal scroll
      return _buildMobileLayout(movies);
    }
  }

  Widget _buildDesktopLayout(List<TopMovie> movies) {
    // Show up to 5 movies in a row for desktop
    final displayMovies = movies.take(5).toList();
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: displayMovies
          .map((movie) => Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  child: FilmPoster(
                    imagePath: movie.primaryImage,
                    title: movie.primaryTitle,
                    isNetworkImage: true,
                  ),
                ),
              ))
          .toList(),
    );
  }

  Widget _buildMobileLayout(List<TopMovie> movies) {
    return SizedBox(
      height: 300,
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        physics: const BouncingScrollPhysics(),
        child: Row(
          children: movies
              .map((movie) => Padding(
                    padding: const EdgeInsets.only(right: 16),
                    child: FilmPoster(
                      imagePath: movie.primaryImage,
                      title: movie.primaryTitle,
                      isNetworkImage: true,
                    ),
                  ))
              .toList(),
        ),
      ),
    );
  }
}
