import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:movie_proj/core/my_colors.dart';
import 'package:movie_proj/core/my_styles.dart';
import 'package:movie_proj/core/my_text.dart';
import 'package:movie_proj/core/spacing.dart';
import 'package:movie_proj/core/widget/movie_app_bar.dart';
import 'package:movie_proj/feature/home/<USER>/top_movies_cubit.dart';
import 'package:movie_proj/feature/home/<USER>/top_moves.dart';
import 'package:movie_proj/feature/home/<USER>/film_avatars.dart';
import 'package:movie_proj/feature/home/<USER>/film_info_widget.dart';
import 'package:movie_proj/feature/home/<USER>/film_poster.dart';
import 'package:movie_proj/feature/home/<USER>/home_most_watched.dart';

class HomeScreen extends StatefulWidget {
  final Function(int) onNavigate;

  const HomeScreen({
    super.key,
    required this.onNavigate,
  });

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  void initState() {
    super.initState();
    // Load movies when the screen is first mounted
    Future.microtask(() => context.read<TopMoviesCubit>().loadTopMovies());
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final horizontalPadding = screenWidth > 600 ? 60.0 : 20.0;

    return Scaffold(
      backgroundColor: MyColors.primaryColor,
      appBar: MovieAppBar(
        currentIndex: 0,
        onNavigate: widget.onNavigate,
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            const Padding(
              padding: EdgeInsets.symmetric(vertical: 20),
              child: Text(
                MyText.mostWatched,
                style: MyStyles.title24White400,
              ),
            ),
            const HomeMostWatched(),
            vSpace(30),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(MyText.tvShows,
                      style: MyStyles.title24White400.copyWith(fontSize: 14)),
                  hSpace(40),
                  Text(MyText.movies,
                      style: MyStyles.title24White400.copyWith(fontSize: 14)),
                  hSpace(40),
                  Text(MyText.newPopular,
                      style: MyStyles.title24White400.copyWith(fontSize: 14)),
                  hSpace(40),
                  Text(MyText.myList,
                      style: MyStyles.title24White400.copyWith(fontSize: 14)),
                  hSpace(40),
                  Text(MyText.browseByLanguage,
                      style: MyStyles.title24White400.copyWith(fontSize: 14)),
                ],
              ),
            ),
            vSpace(30),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
              child: BlocBuilder<TopMoviesCubit, TopMoviesState>(
                builder: (context, state) {
                  if (state is TopMoviesLoading) {
                    return const Center(
                      child: CircularProgressIndicator(),
                    );
                  } else if (state is TopMoviesError) {
                    return Center(
                      child: Text(
                        state.message,
                        style: MyStyles.title24White400
                            .copyWith(color: Colors.red),
                      ),
                    );
                  } else if (state is TopMoviesLoaded) {
                    final movies = state.movies;
                    return screenWidth > 900
                        ? Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: _buildMoviePosters(movies),
                          )
                        : SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: Row(
                              children: _buildMoviePosters(movies)
                                  .map(
                                    (poster) => Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 10),
                                      child: poster,
                                    ),
                                  )
                                  .toList(),
                            ),
                          );
                  }
                  return const SizedBox();
                },
              ),
            ),
            vSpace(50),
            const FilmInfoWidget(
              text: MyText.newRelease,
            ),
            vSpace(30),
            const FilmInfoWidget(text: MyText.pickedForYou),
            vSpace(50),
            const FilmAvatars(),
            vSpace(30),
            dSpace(),
            vSpace(20)
          ],
        ),
      ),
    );
  }

  List<Widget> _buildMoviePosters(List<TopMovie> movies) {
    return movies
        .map((movie) => FilmPoster(
              imagePath: movie.primaryImage,
              title: movie.primaryTitle,
              isNetworkImage: true,
            ))
        .toList();
  }
}
